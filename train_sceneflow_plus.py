import os
import hydra
import torch
from tqdm import tqdm
import torch.optim as optim
from core.utils.utils import InputPadder
from core.monste_plus import Monster  
from omegaconf import OmegaConf
import torch.nn as nn
import torch.nn.functional as F
from accelerate import Accelerator
import core.stereo_datasets as datasets
from accelerate.utils import set_seed
from accelerate.logging import get_logger
from accelerate import DataLoaderConfiguration
from accelerate.utils import DistributedDataParallelKwargs

import matplotlib
import matplotlib.pyplot as plt
import numpy as np
import wandb
from pathlib import Path


# 🔥 导入 Muon 优化器
try:
    from core.muon_optimizer import MuonWithAuxAdam
    MUON_AVAILABLE = True
    print("✅ Muon 优化器导入成功")
except ImportError:
    print("❌ Muon 优化器导入失败，将回退到 AdamW")
    MUON_AVAILABLE = False


def scale_invariant_loss(pred_disp, gt_disp, valid_mask, lambda_reg=0.85):
    """
    标准SiLog损失函数 (Scale-Invariant Logarithmic Loss)
    只对标签视差（gt_disp）做中位数归一化，预测视差不归一化。
    """
    eps = 1e-8
    pred_disp = torch.clamp(pred_disp, min=eps)
    gt_disp = torch.clamp(gt_disp, min=eps)
    valid_mask = valid_mask.bool()

    if valid_mask.sum() == 0:
        return torch.tensor(0.0, device=pred_disp.device)

    # 只对标签视差做归一化（只在有效区域内取中位数）
    gt_disp_norm = gt_disp.clone()
    B = pred_disp.shape[0]
    for b in range(B):
        mask_b = valid_mask[b, 0]
        if mask_b.sum() > 0:
            gt_med = gt_disp[b, 0][mask_b].median()
            gt_disp_norm[b, 0] = gt_disp[b, 0] / (gt_med + eps)
        else:
            gt_disp_norm[b, 0] = gt_disp[b, 0]

    # 计算对数差异 d_i = log(pred) - log(gt)
    log_diff = torch.log(pred_disp[valid_mask]) - torch.log(gt_disp_norm[valid_mask])
    n = valid_mask.sum().float()
    term1 = torch.sum(log_diff ** 2) / n
    term2 = lambda_reg * (torch.sum(log_diff) ** 2) / (n ** 2)
    silog_loss = term1 - term2
    return silog_loss


def pmc_loss(prob, disparity, corr_radius=4.0, epsilon=0.1, mask=None, prob_threshold=0.8):
    """
    支持亚像素精度的PMC损失 - 使用阈值权重替代高斯权重

    Args:
        prob: 概率体，形状为 [b, n_disp, h, w]
        disparity: 真实视差图（可以是亚像素），形状为 [b, 1, h, w] 或 [b, h, w]
        corr_radius: 相关半径，用于确定有效视差邻域
        epsilon: 损失函数中的容忍度
        mask: 有效像素mask，可选
        prob_threshold: 概率阈值，超过此值则不计算损失（默认0.8）

    Returns:
        loss: PMC损失值
    """
    b, n_disp, h, w = prob.shape
    device = prob.device

    # 保证disparity为 [b, 1, h, w]
    if disparity.dim() == 3:
        disparity = disparity.unsqueeze(1)

    # 创建视差索引
    disp_indices = torch.arange(n_disp, dtype=torch.float32, device=device)
    disp_indices = disp_indices.view(1, -1, 1, 1).expand(b, -1, h, w)

    # 扩展真实视差
    gt_disparity = disparity.expand(-1, n_disp, -1, -1)

    # 计算距离
    distance = torch.abs(disp_indices - gt_disparity)

    # 🔥 新的阈值权重系统：替代高斯权重
    # Step 1: 创建相关半径内的二值掩码
    within_radius = (distance <= corr_radius).float()

    # Step 2: 计算相关半径内的概率质量
    prob_within_radius = torch.sum(prob * within_radius, dim=1, keepdim=True)

    # Step 3: 应用阈值权重
    # 如果概率 > threshold，则权重为0（不计算损失）
    # 否则权重为1（计算完整损失）
    loss_weight = (prob_within_radius <= prob_threshold).float()

    # Step 4: 计算损失（使用相关半径内的概率质量）
    loss_map = loss_weight * torch.clamp(1.0 - prob_within_radius - epsilon, min=0.0)

    if mask is not None:
        loss_map = loss_map * mask.unsqueeze(1)
        valid_pixels = torch.sum(mask.unsqueeze(1))
    else:
        valid_pixels = b * h * w

    loss = torch.sum(loss_map) / (valid_pixels + 1e-8)

    return loss



def check_horizontal_monotonicity(disp):
    """检查同一行相邻像素的视差单调性约束 disp[h,w+1] - disp[h,w] < 1
    输入: (b, 1, h, w) 的视差图
    输出: (b, 1, h, w) 的布尔mask，False表示违反约束的位置"""
    b, c, h, w = disp.shape
    mask = torch.ones_like(disp, dtype=torch.bool)
    
    # 计算相邻像素差值（沿宽度方向）
    diff = torch.diff(disp, dim=3)  # shape: (b, 1, h, w-1)
    
    # 找到违反约束的位置（差值 >= 1）
    violation = diff > 1.1
    
    # 在mask中标记违反约束的位置
    mask[..., 1:] = ~violation
    
    return mask

def geometric_consistency_loss(disp_pred, disp_gt):
    """计算预测视差图在标签mask区域内的几何约束损失
    
    参数:
        disp_pred: (b, 1, h, w) 的预测视差图
        disp_gt: (b, 1, h, w) 的真实视差图
    返回:
        水平单调性约束损失值
    """
    # 生成约束mask（True表示需要满足约束的区域）
    with torch.no_grad():
        constraint_mask = check_horizontal_monotonicity(disp_gt)  # shape: (b, 1, h, w)
    
    # 计算预测图的相邻差值
    pred_diff = torch.diff(disp_pred, dim=3)  # shape: (b, 1, h, w-1)
    
    # 在约束区域内计算违反约束的量（超过1的部分）
    violation = torch.relu(pred_diff - 1.1)  # 只考虑正向差值超过1的部分
    
    # 只在约束mask对应的区域计算损失
    constraint_diff_mask = constraint_mask[..., 1:]  # 对齐差值张量维度
    valid_violations = violation * constraint_diff_mask
    
    # 计算平均损失
    geometric_loss = valid_violations.sum() / (constraint_diff_mask.sum() + 1e-8)
    
    # 添加对负视差值的惩罚项
    # 负视差值在物理上是不合理的，应该被惩罚
    negative_penalty = torch.relu(-disp_pred)
    # 只在有效视差区域内计算负值惩罚
    valid_mask = (disp_gt > 0) & ~torch.isnan(disp_gt)
    negative_loss = (negative_penalty * valid_mask).sum() / (valid_mask.sum() + 1e-8)
    
    # 组合几何约束损失和负视差惩罚损失
    total_loss = geometric_loss + negative_loss
    
    return total_loss

def sequence_loss(disp_preds, disp_init_pred, prob, disp_gt, occ_mask_gt, valid, loss_gamma=0.9, max_disp=768, corr_radius=4.0):
    
    valid_main = valid
    valid_down = valid[:, ::4, ::4]
    occ_mask_gt_down = occ_mask_gt[:, :, ::4, ::4]

    # === 2. 初始遮挡mask损失 ===
    # mask_occ = valid_down.bool().unsqueeze(1)  # [B,1,H,W]
    # occ_diff = torch.abs(occ_mask - occ_mask_gt_down)
    # occ_loss = (occ_diff * mask_occ).sum() / (mask_occ.sum() + 1e-8)

    # === 3. 概率体单峰损失, 只在非遮挡区域计算 ===
    prob_loss = pmc_loss(prob, disp_gt[:, :, ::4, ::4]/4.0, corr_radius=corr_radius, epsilon=0.1, mask=valid_down.bool())

    # === 4. 有效区域mask准备 ===
    mag = torch.sum(disp_gt**2, dim=1).sqrt()
    valid_mask = ((valid_main >= 0.5) & (mag < max_disp)).unsqueeze(1)
    assert valid_mask.shape == disp_gt.shape, [valid_mask.shape, disp_gt.shape]

    # === 5. 初始预测损失 ===
    mag_init = torch.sum(disp_init_pred**2, dim=1).sqrt()
    init_valid = ((valid_mask.squeeze(1) >= 0.5) & (mag_init < 768)).unsqueeze(1)
    disp_loss = 0.2 * F.smooth_l1_loss(disp_init_pred[init_valid.bool()], disp_gt[init_valid.bool()], reduction='mean')
    geo_loss = 0.2 * geometric_consistency_loss(disp_init_pred, disp_gt)
    
    # === 6. 多阶段主视差损失 ===
    n_predictions = len(disp_preds)
    assert n_predictions >= 1
    for i in range(n_predictions):
        adjusted_loss_gamma = loss_gamma**(15/(n_predictions - 1))
        i_weight = adjusted_loss_gamma**(n_predictions - i - 1)
        disp_pred = disp_preds[i]
        disp_error = (disp_pred - disp_gt).abs()
        stage_valid = valid_mask.bool() & ~torch.isnan(disp_error)
        if stage_valid.sum() > 0:
            disp_loss += i_weight * disp_error[stage_valid].mean()
        geo_loss += i_weight * geometric_consistency_loss(disp_pred, disp_gt)

    # # === 7. 遮挡损失 ===
    # for i in range(len(occ_mask_preds)):
    #     adjusted_loss_gamma = loss_gamma**(15/(len(occ_mask_preds) - 1))
    #     i_weight = adjusted_loss_gamma**(len(occ_mask_preds) - i - 1)

    #     occ_pred = occ_mask_preds[i]
    #     occ_diff = torch.abs(occ_pred - occ_mask_gt_down)
    #     mask_occ = valid_down.bool().unsqueeze(1)
    #     occ_loss += (occ_diff * mask_occ).sum() / (mask_occ.sum() + 1e-8)

    with torch.no_grad():
        # === 10. 评估指标 ===
        epe = torch.sum((disp_preds[-1] - disp_gt)**2, dim=1).sqrt()
        epe = epe.view(-1)[valid_mask.view(-1)]
        if valid_mask.bool().sum() == 0:
            # 保持与非空情况相同的维度（1D张量）
            epe = torch.zeros(1, device=disp_gt.device)
        metrics = {
            'train/epe': epe.mean(),
            'train/1px': (epe < 1).float().mean(),
            'train/3px': (epe < 3).float().mean(),
            'train/5px': (epe < 5).float().mean(),
            'train/disp_loss': disp_loss,
            # 'train/occ_loss': occ_loss,
            'train/prob_loss': prob_loss,
            'train/geo_loss': geo_loss,
        }
    # === 9. 总损失 ===
    total_loss = disp_loss + 0.1 * geo_loss  + 0.1 * prob_loss 
    return total_loss, metrics

# def fetch_optimizer(args, model):
#     """
#     创建优化器和学习率调度器 - 分组学习率版本
    
#     设置分组学习率：
#     - Depth Anything V2部分 (mono_encoder, mono_decoder, feat_decoder): 标准学习率的0.8倍
#     - 其他参数 (立体匹配模块等): 标准学习率
#     - BatchNorm: 冻结 (通过freeze_bn()方法控制)
#     """
    
#     # 🔥 分组参数：Depth Anything部分 vs 其他部分
#     depth_anything_params = []
#     other_params = []
    
#     # 分别收集不同模块的参数
#     for name, param in model.named_parameters():
#         if not param.requires_grad:
#             continue
            
#         # Depth Anything V2相关参数
#         if any(prefix in name for prefix in ['mono_encoder', 'mono_decoder', 'feat_decoder']):
#             depth_anything_params.append(param)
#         else:
#             # 其他参数（立体匹配模块等）
#             other_params.append(param)
    
#     # 统计参数数量
#     depth_anything_count = sum(p.numel() for p in depth_anything_params)
#     other_count = sum(p.numel() for p in other_params)
#     total_count = depth_anything_count + other_count
    
#     print(f"\n🎯 分组学习率设置:")
#     print(f"   📊 Depth Anything V2参数: {depth_anything_count:,} ({depth_anything_count/total_count*100:.1f}%)")
#     print(f"   🎪 - 学习率: {args.lr * 0.8:.2e} (标准学习率的0.8倍)")
#     print(f"   📊 其他模块参数: {other_count:,} ({other_count/total_count*100:.1f}%)")
#     print(f"   🎪 - 学习率: {args.lr:.2e} (标准学习率)")
#     print(f"   📊 总参数: {total_count:,}")
#     print(f"   ❄️  BatchNorm: 冻结 (通过freeze_bn()控制)")
    
#     # 显示各模块的参数统计
#     print(f"\n📋 各模块参数统计:")
#     module_stats = {}
#     for name, param in model.named_parameters():
#         if not param.requires_grad:
#             continue
#         module_name = name.split('.')[0]
#         if module_name not in module_stats:
#             module_stats[module_name] = {'count': 0, 'params': 0}
#         module_stats[module_name]['count'] += 1
#         module_stats[module_name]['params'] += param.numel()
    
#     for module_name, stats in sorted(module_stats.items()):
#         param_ratio = stats['params'] / total_count * 100
#         is_depth_anything = any(prefix in module_name for prefix in ['mono_encoder', 'mono_decoder', 'feat_decoder'])
#         lr_symbol = "🔥" if is_depth_anything else "⚡"
#         lr_ratio = "0.8x" if is_depth_anything else "1.0x"
#         print(f"   {lr_symbol} {module_name}: {stats['params']:,} 参数 ({param_ratio:.1f}%) - {lr_ratio}")
    
#     # 创建参数组
#     param_groups = [
#         {
#             'params': depth_anything_params,
#             'lr': args.lr * 0.8,  # Depth Anything部分使用0.8倍学习率
#             'name': 'depth_anything'
#         },
#         {
#             'params': other_params,
#             'lr': args.lr,  # 其他参数使用标准学习率
#             'name': 'stereo_matching'
#         }
#     ]
    
#     # 过滤空的参数组
#     param_groups = [group for group in param_groups if len(group['params']) > 0]
    
#     # 创建优化器
#     optimizer = optim.AdamW(param_groups, weight_decay=args.wdecay, eps=1e-8)
    
#     print(f"\n✅ 优化器创建完成:")
#     print(f"   - 参数组数量: {len(param_groups)}")
#     for i, group in enumerate(param_groups):
#         group_param_count = sum(p.numel() for p in group['params'])
#         print(f"   - 组{i+1} ({group['name']}): {group_param_count:,} 参数, 学习率: {group['lr']:.2e}")

#     # 标准学习率调度器
#     scheduler = optim.lr_scheduler.OneCycleLR(
#         optimizer, 
#         max_lr=[group['lr'] for group in param_groups],  # 为每个参数组设置对应的最大学习率
#         total_steps=args.total_step + 100,
#         pct_start=0.01, 
#         cycle_momentum=False, 
#         anneal_strategy='linear'
#     )

#     return optimizer, scheduler



def fetch_optimizer(args, model):
    """
    优化器选择函数：优先使用 Muon，不可用时回退到 AdamW
    """
    if MUON_AVAILABLE:
        return fetch_optimizer_muon(args, model)
    else:
        return fetch_optimizer_adamw(args, model)


def fetch_optimizer_muon(args, model):
    """
    🔥 Muon 混合优化器策略（基于官方实现）
    
    参数分组：
    - 2D+隐藏层权重: 使用 Muon 优化器
    - 1D参数 + embedding/输出层: 使用 AdamW 优化器
    
    根据 Muon 论文建议：
    - Muon 使用自适应学习率调整
    - AdamW 保持标准学习率
    """
    if not MUON_AVAILABLE:
        print("⚠️  Muon 不可用，回退到标准 AdamW 优化器")
        return fetch_optimizer_adamw(args, model)
    
    # 🔥 按照官方 Muon 实现的参数分组策略
    muon_params = []      # 2D+ 参数用 Muon（隐藏层）
    adamw_params = []     # 其他参数用 AdamW
    
    # 统计变量
    muon_param_count = 0
    adamw_param_count = 0
    frozen_param_count = 0
    
    print(f"\n🔍 Muon 优化器参数分组分析:")
    
    for name, param in model.named_parameters():
        param_count = param.numel()
        
        if not param.requires_grad:
            frozen_param_count += param_count
            continue
            
        # 按照官方 Muon 建议：
        # - 2D+ 参数且不是 embedding/head 层 → Muon
        # - 其他参数 → AdamW
        if (param.ndim >= 2 and 
            not any(x in name.lower() for x in ['embed', 'head', 'classifier', 'mono_encoder', 'mono_decoder'])):
            muon_params.append(param)
            muon_param_count += param_count
        else:
            adamw_params.append(param)
            adamw_param_count += param_count
    
    total_trainable = muon_param_count + adamw_param_count
    total_params = total_trainable + frozen_param_count
    
    print(f"   🎯 Muon 参数: {muon_param_count:,} ({muon_param_count/total_trainable*100:.1f}% of trainable)")
    print(f"   ⚙️  AdamW 参数: {adamw_param_count:,} ({adamw_param_count/total_trainable*100:.1f}% of trainable)")
    print(f"   ❄️  冻结参数: {frozen_param_count:,} ({frozen_param_count/total_params*100:.1f}% of total)")
    print(f"   📊 总参数: {total_params:,}")
    
    muon_lr = args.lr             
    
    # 创建 Muon 混合优化器（使用官方 API）
    optimizer = MuonWithAuxAdam(
        lr=muon_lr,
        wd=args.wdecay,
        muon_params=muon_params,
        adamw_params=adamw_params,
        momentum=0.95,
        nesterov=True,
        ns_steps=5,
        adamw_betas=(0.9, 0.95),
        adamw_eps=1e-8,
    )
    
    print(f"✅ Muon 混合优化器创建成功！")
    print(f"   - {len(muon_params)} 个权重使用 Muon")
    print(f"   - {len(adamw_params)} 个参数使用 AdamW")
    
    # 标准学习率调度器
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer, 
        muon_lr,  # 使用 Muon 学习率作为主要学习率
        args.total_step + 100,
        pct_start=0.01, 
        cycle_momentum=False,
        anneal_strategy='linear'
    )
    
    return optimizer, scheduler

def fetch_optimizer_adamw(args, model):
    """
    回退的 AdamW 优化器（当 Muon 不可用时）
    """
    trainable_params = [p for p in model.parameters() if p.requires_grad]
    
    trainable_count = sum(p.numel() for p in trainable_params)
    total_params = sum(p.numel() for p in model.parameters())
    frozen_count = total_params - trainable_count
    
    print(f"\n🎯 AdamW 优化器参数设置:")
    print(f"   ✅ 可训练参数: {trainable_count:,} ({trainable_count/total_params*100:.1f}%)")
    print(f"   ❄️  冻结参数: {frozen_count:,} ({frozen_count/total_params*100:.1f}%)")
    print(f"   📊 总参数: {total_params:,}")
    print(f"   🎪 学习率: {args.lr:.2e}")
    
    optimizer = optim.AdamW(trainable_params, lr=args.lr, weight_decay=args.wdecay, eps=1e-8)

    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer, 
        args.lr,
        args.total_step + 100,
        pct_start=0.01, 
        cycle_momentum=False, 
        anneal_strategy='linear'
    )

    return optimizer, scheduler



@hydra.main(version_base=None, config_path='config', config_name='train_sceneflow')
def main(cfg):
    set_seed(cfg.seed)
    logger = get_logger(__name__)
    # 🔥 重新启用模型保存文件夹创建（用户需要保存模型权重）
    Path(cfg.save_path).mkdir(exist_ok=True, parents=True)
    kwargs = DistributedDataParallelKwargs(find_unused_parameters=True)
    
    # 🔥 禁用wandb但启用模型保存
    print("🚫 已禁用wandb功能（避免创建wandb文件夹）")
    print("✅ 已启用模型权重保存功能")
    
    # 始终使用简单的加速器配置，不启用wandb
    accelerator = Accelerator(
        mixed_precision='bf16', 
        dataloader_config=DataLoaderConfiguration(use_seedable_sampler=True), 
        kwargs_handlers=[kwargs], 
        step_scheduler_with_optimizer=False
    )
    
    # 🔥 完全跳过wandb相关代码
    # 检查是否为测试模式（total_step < 100则认为是测试）
    # is_test_mode = getattr(cfg, 'total_step', 50000) < 100

    # 数据集
    train_dataset = datasets.fetch_dataloader(cfg)
    train_loader = torch.utils.data.DataLoader(
        train_dataset, 
        batch_size=cfg.batch_size//cfg.num_gpu,
        pin_memory=False,  # 🔥 改为False减少内存占用
        shuffle=True, 
        num_workers=2,     # 🔥 改为0避免子进程内存问题
        drop_last=True
    )

    # 创建Monster Plus模型
    model = Monster(cfg)
    
    # 🔥 确认全参数训练状态
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"\n🎯 模型参数状态确认:")
    print(f"   📊 总参数: {total_params:,}")
    print(f"   ✅ 可训练参数: {trainable_params:,} ({trainable_params/total_params*100:.1f}%)")
    print(f"   🎪 训练模式: 全参数训练 + BatchNorm冻结")
    
    # 加载预训练权重
    if not cfg.restore_ckpt.endswith("None"):
        assert cfg.restore_ckpt.endswith(".pth")
        print(f"Loading checkpoint from {cfg.restore_ckpt}")
        assert os.path.exists(cfg.restore_ckpt)
        checkpoint = torch.load(cfg.restore_ckpt, map_location='cpu')
        ckpt = dict()
        if 'state_dict' in checkpoint.keys():
            checkpoint = checkpoint['state_dict']
        for key in checkpoint:
            ckpt[key.replace('module.', '')] = checkpoint[key]

        # 🔥 智能权重加载：只加载存在且维度匹配的权重
        model_state = model.state_dict()
        model_keys = set(model_state.keys())
        ckpt_keys = set(ckpt.keys())
        
        print(f"\n🔍 权重加载详细信息:")
        print(f"📊 模型中的权重数量: {len(model_keys)}")
        print(f"📊 checkpoint中的权重数量: {len(ckpt_keys)}")
        
        # 预处理：检查维度匹配
        matched_keys = []
        shape_mismatch_keys = []
        missing_keys = []
        extra_keys = []
        
        # 检查每个模型权重
        for key in model_keys:
            if key in ckpt_keys:
                model_shape = model_state[key].shape
                ckpt_shape = ckpt[key].shape
                
                if model_shape == ckpt_shape:
                    matched_keys.append(key)
                else:
                    shape_mismatch_keys.append((key, model_shape, ckpt_shape))
            else:
                missing_keys.append(key)
        
        # 检查checkpoint中多余的权重
        for key in ckpt_keys:
            if key not in model_keys:
                extra_keys.append(key)
        
        # 创建过滤后的checkpoint（只包含维度匹配的权重）
        filtered_ckpt = {}
        for key in matched_keys:
            filtered_ckpt[key] = ckpt[key]
        
        print(f"✅ 维度匹配的权重: {len(matched_keys)}")
        print(f"⚠️  维度不匹配的权重: {len(shape_mismatch_keys)}")
        print(f"❌ 模型中缺失的权重: {len(missing_keys)}")
        print(f"ℹ️  checkpoint中多余的权重: {len(extra_keys)}")
        
        # 显示维度不匹配的详细信息
        if shape_mismatch_keys:
            print(f"\n⚠️  维度不匹配的权重详情:")
            for key, model_shape, ckpt_shape in sorted(shape_mismatch_keys):
                print(f"   - {key}: 模型{model_shape} vs checkpoint{ckpt_shape}")
        
        # 显示缺失权重的前几个（避免输出过长）
        if missing_keys:
            print(f"\n❌ 模型中缺失的权重 (前10个):")
            for key in sorted(missing_keys)[:10]:
                print(f"   - {key}")
            if len(missing_keys) > 10:
                print(f"   ... 还有 {len(missing_keys)-10} 个权重")
        
        # 按模块分类显示加载情况
        print(f"\n📋 按模块分类的权重加载情况:")
        modules = {}
        for key in model_keys:
            module_name = key.split('.')[0]
            if module_name not in modules:
                modules[module_name] = {'total': 0, 'loaded': 0, 'shape_mismatch': 0}
            modules[module_name]['total'] += 1
            if key in matched_keys:
                modules[module_name]['loaded'] += 1
            elif any(key == mkey for mkey, _, _ in shape_mismatch_keys):
                modules[module_name]['shape_mismatch'] += 1
        
        for module_name, stats in sorted(modules.items()):
            total = stats['total']
            loaded = stats['loaded']
            mismatch = stats['shape_mismatch']
            missing = total - loaded - mismatch
            
            loaded_ratio = loaded / total * 100
            status = "✅" if loaded_ratio == 100 else "⚠️" if loaded_ratio > 0 else "❌"
            
            info_parts = [f"{loaded}/{total}"]
            if mismatch > 0:
                info_parts.append(f"{mismatch}维度不匹配")
            if missing > 0:
                info_parts.append(f"{missing}缺失")
                
            print(f"   {status} {module_name}: {', '.join(info_parts)} ({loaded_ratio:.1f}%)")

        # 🔥 安全加载：只加载维度匹配的权重
        missing_keys, unexpected_keys = model.load_state_dict(filtered_ckpt, strict=False)
        
        print(f"\n🎯 智能权重加载结果:")
        print(f"✅ 成功加载 {len(matched_keys)} 个维度匹配的权重")
        if shape_mismatch_keys:
            print(f"⚠️  跳过 {len(shape_mismatch_keys)} 个维度不匹配的权重")
        if missing_keys:
            print(f"❌ 缺失 {len(missing_keys)} 个权重（使用随机初始化）")
        if extra_keys:
            print(f"ℹ️  忽略 {len(extra_keys)} 个checkpoint中多余的权重")
        
        # 计算加载覆盖率
        total_model_params = len(model_keys)
        loaded_params = len(matched_keys)
        coverage_rate = loaded_params / total_model_params * 100
        
        print(f"📊 权重加载覆盖率: {loaded_params}/{total_model_params} ({coverage_rate:.1f}%)")
        
        if coverage_rate >= 90:
            print(f"🎉 权重加载完成！覆盖率优秀 ({coverage_rate:.1f}%)")
        elif coverage_rate >= 70:
            print(f"✅ 权重加载完成！覆盖率良好 ({coverage_rate:.1f}%)")
        elif coverage_rate >= 50:
            print(f"⚠️  权重加载完成！覆盖率一般 ({coverage_rate:.1f}%)")
        else:
            print(f"❌ 权重加载完成！覆盖率较低 ({coverage_rate:.1f}%)，建议检查checkpoint匹配性")
        
        print(f"📂 Checkpoint: {cfg.restore_ckpt}")
        del ckpt, checkpoint, filtered_ckpt
    
    optimizer, lr_scheduler = fetch_optimizer(cfg, model)
    train_loader, model, optimizer, lr_scheduler = accelerator.prepare(
        train_loader, model, optimizer, lr_scheduler
    )
    model.to(accelerator.device)

    total_step = 0
    should_keep_training = True
    
    while should_keep_training:
        active_train_loader = train_loader

        model.train()
        # 🔥 全参数训练但保持BatchNorm冻结（兼容单卡和多卡训练）
        # if hasattr(model, 'module'):
        #     model.module.freeze_bn()
        # else:
        #     model.freeze_bn()
        
        for data in tqdm(active_train_loader, dynamic_ncols=True, disable=not accelerator.is_main_process):
            _, left, right, disp_gt, occ_mask_gt, valid = [x for x in data]
            
            with accelerator.autocast():
                disp_init_pred, disp_preds, prob = model(left, right, iters=cfg.train_iters)  # 🚫 移除disp_mono返回值
                

            loss, metrics = sequence_loss(
                disp_preds, disp_init_pred, prob, disp_gt, occ_mask_gt, valid,  # 🚫 移除disp_mono参数
                max_disp=cfg.max_disp,
                corr_radius=cfg.corr_radius
            )
            
            accelerator.backward(loss)
            accelerator.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            lr_scheduler.step()
            optimizer.zero_grad()

            total_step += 1
            loss = accelerator.reduce(loss.detach(), reduction='mean')
            metrics = accelerator.reduce(metrics, reduction='mean')
            
            # 每100步打印训练指标
            if total_step % 100 == 0 and accelerator.is_main_process:
                print(f"\n=== Step {total_step} Training Metrics ===")
                print(f"Disp Loss: {loss.item():.6f}")
                print(f"LR (Depth Anything): {optimizer.param_groups[0]['lr']:.2e}")
                if len(optimizer.param_groups) > 1:
                    print(f"LR (Stereo Matching): {optimizer.param_groups[1]['lr']:.2e}")
                print(f"EPE: {metrics['train/epe'].item():.4f}")
                print(f"1px: {metrics['train/1px'].item():.4f}")
                print(f"3px: {metrics['train/3px'].item():.4f}")
                print(f"5px: {metrics['train/5px'].item():.4f}")
                # print(f"Occ Loss: {metrics['train/occ_loss'].item():.6f}")
                print(f"Prob Loss: {metrics['train/prob_loss'].item():.6f}")
                print(f"Geo Loss: {metrics['train/geo_loss'].item():.6f}")
                print("=" * 40)


            # 🔥 重新启用模型保存功能
            # 保存模型
            if (total_step > 0) and (total_step % cfg.save_frequency == 0):
                if accelerator.is_main_process:
                    save_path = Path(cfg.save_path + '/%d_plus.pth' % (total_step))
                    model_save = accelerator.unwrap_model(model)
                    torch.save(model_save.state_dict(), save_path)
                    print(f"Model saved to {save_path}")
                    del model_save

            if total_step == cfg.total_step:
                should_keep_training = False
                break

    # 🔥 重新启用最终模型保存功能
    # 保存最终模型
    if accelerator.is_main_process:
        save_path = Path(cfg.save_path + '/final_plus.pth')
        model_save = accelerator.unwrap_model(model)
        torch.save(model_save.state_dict(), save_path)
        print(f"Final model saved to {save_path}")
        del model_save
    
    # 🔥 跳过accelerator结束训练（因为没有启用wandb）
    # if not is_test_mode:
    #     accelerator.end_training()
    
    print("🎉 训练完成！模型权重已保存，但未创建wandb文件夹。")

if __name__ == '__main__':
    main() 