# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.


# Inspired by https://github.com/DepthAnything/Depth-Anything-V2


import os
from typing import List, Dict, Tuple, Union, Optional

import torch
import torch.nn as nn
import torch.nn.functional as F
from .head_act import activate_head
from .utils import create_uv_grid, position_grid_to_embed


class DPTHead(nn.Module):
    """
    DPT  Head for dense prediction tasks.

    This implementation follows the architecture described in "Vision Transformers for Dense Prediction"
    (https://arxiv.org/abs/2103.13413). The DPT head processes features from a vision transformer
    backbone and produces dense predictions by fusing multi-scale features.

    Args:
        dim_in (int): Input dimension (channels).
        patch_size (int, optional): Patch size. Default is 14.
        output_dim (int, optional): Number of output channels. Default is 4.
        activation (str, optional): Activation type. Default is "inv_log".
        conf_activation (str, optional): Confidence activation type. Default is "expp1".
        features (int, optional): Feature channels for intermediate representations. Default is 256.
        out_channels (List[int], optional): Output channels for each intermediate layer.
        intermediate_layer_idx (List[int], optional): Indices of layers from aggregated tokens used for DPT.
        pos_embed (bool, optional): Whether to use positional embedding. Default is True.
        feature_only (bool, optional): If True, return features only without the last several layers and activation head. Default is False.
        down_ratio (int, optional): Downscaling factor for the output resolution. Default is 1.
    """

    def __init__(
        self,
        dim_in: int,
        patch_size: int = 14,
        output_dim: int = 4,
        activation: str = "inv_log",
        conf_activation: str = "expp1",
        features: int = 256,
        out_channels: List[int] = [256, 512, 1024, 1024],
        intermediate_layer_idx: List[int] = [4, 11, 17, 23],
        pos_embed: bool = True,
        feature_only: bool = False,
        down_ratio: int = 1,
    ) -> None:
        super(DPTHead, self).__init__()
        self.patch_size = patch_size
        self.activation = activation
        self.conf_activation = conf_activation
        self.pos_embed = pos_embed
        self.feature_only = feature_only
        self.down_ratio = down_ratio
        self.intermediate_layer_idx = intermediate_layer_idx

        self.norm = nn.LayerNorm(dim_in)

        # Projection layers for each output channel from tokens.
        # projects: List[nn.Conv2d]，每个卷积层将dim_in维特征映射到对应的out_channels维
        self.projects = nn.ModuleList(
            [
                nn.Conv2d(
                    in_channels=dim_in,
                    out_channels=oc,
                    kernel_size=1,
                    stride=1,
                    padding=0,
                )
                for oc in out_channels
            ]
        )

        # Resize layers for upsampling feature maps.
        # resize_layers: List[nn.Module]，用于上采样特征图到统一尺寸
        self.resize_layers = nn.ModuleList(
            [
                nn.ConvTranspose2d(
                    in_channels=out_channels[0], out_channels=out_channels[0], kernel_size=4, stride=4, padding=0
                ),
                nn.ConvTranspose2d(
                    in_channels=out_channels[1], out_channels=out_channels[1], kernel_size=2, stride=2, padding=0
                ),
                nn.Identity(),
                nn.Conv2d(
                    in_channels=out_channels[3], out_channels=out_channels[3], kernel_size=3, stride=2, padding=1
                ),
            ]
        )

        self.scratch = _make_scratch(
            out_channels,
            features,
            expand=False,
        )

        # Attach additional modules to scratch.
        self.scratch.stem_transpose = None
        self.scratch.refinenet1 = _make_fusion_block(features)
        self.scratch.refinenet2 = _make_fusion_block(features)
        self.scratch.refinenet3 = _make_fusion_block(features)
        self.scratch.refinenet4 = _make_fusion_block(features, has_residual=False)

        head_features_1 = features
        head_features_2 = 32

        if feature_only:
            self.scratch.output_conv1 = nn.Conv2d(head_features_1, head_features_1, kernel_size=3, stride=1, padding=1)
        else:
            self.scratch.output_conv1 = nn.Conv2d(
                head_features_1, head_features_1 // 2, kernel_size=3, stride=1, padding=1
            )
            conv2_in_channels = head_features_1 // 2

            self.scratch.output_conv2 = nn.Sequential(
                nn.Conv2d(conv2_in_channels, head_features_2, kernel_size=3, stride=1, padding=1),
                nn.ReLU(inplace=True),
                nn.Conv2d(head_features_2, output_dim, kernel_size=1, stride=1, padding=0),
            )

    def forward(
        self,
        aggregated_tokens_list: List[torch.Tensor],
        images: torch.Tensor,
        patch_start_idx: int,
        frames_chunk_size: int = 8,
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        Forward pass through the DPT head, supports processing by chunking frames.
        Args:
            aggregated_tokens_list (List[Tensor]): List of token tensors from different transformer layers.
                Each element has shape [B, S*P, 2*C] where:
                - B: batch size
                - S: sequence length
                - P: number of tokens per image
                - C: embedding dimension
            images (Tensor): Input images with shape [B, S, 3, H, W], in range [0, 1].
            patch_start_idx (int): Starting index for patch tokens in the token sequence.
                Used to separate patch tokens from other tokens (e.g., camera or register tokens).
            frames_chunk_size (int, optional): Number of frames to process in each chunk.
                If None or larger than S, all frames are processed at once. Default: 8.

        Returns:
            Tensor or Tuple[Tensor, Tensor]:
                - If feature_only=True: Feature maps with shape [B, S, C, H, W]
                - Otherwise: Tuple of (predictions, confidence) both with shape [B, S, 1, H, W]
        """
        # images: [B, S, 3, H, W]
        B, S, _, H, W = images.shape

        # If frames_chunk_size is not specified or greater than S, process all frames at once
        if frames_chunk_size is None or frames_chunk_size >= S:
            return self._forward_impl(aggregated_tokens_list, images, patch_start_idx)

        # Otherwise, process frames in chunks to manage memory usage
        assert frames_chunk_size > 0

        # Process frames in batches
        all_preds = []
        all_conf = []

        for frames_start_idx in range(0, S, frames_chunk_size):
            frames_end_idx = min(frames_start_idx + frames_chunk_size, S)

            # Process batch of frames
            if self.feature_only:
                # chunk_output: [B, frames_chunk_size, C, H, W]
                chunk_output = self._forward_impl(
                    aggregated_tokens_list, images, patch_start_idx, frames_start_idx, frames_end_idx
                )
                all_preds.append(chunk_output)
            else:
                # chunk_preds: [B, frames_chunk_size, 1, H, W]
                # chunk_conf: [B, frames_chunk_size, 1, H, W]
                chunk_preds, chunk_conf = self._forward_impl(
                    aggregated_tokens_list, images, patch_start_idx, frames_start_idx, frames_end_idx
                )
                all_preds.append(chunk_preds)
                all_conf.append(chunk_conf)

        # Concatenate results along the sequence dimension
        if self.feature_only:
            # 返回: [B, S, C, H, W]
            return torch.cat(all_preds, dim=1)
        else:
            # 返回: ([B, S, 1, H, W], [B, S, 1, H, W])
            return torch.cat(all_preds, dim=1), torch.cat(all_conf, dim=1)

    def _forward_impl(
        self,
        aggregated_tokens_list: List[torch.Tensor],
        images: torch.Tensor,
        patch_start_idx: int,
        frames_start_idx: int = None,
        frames_end_idx: int = None,
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        Implementation of the forward pass through the DPT head.

        This method processes a specific chunk of frames from the sequence.

        Args:
            aggregated_tokens_list (List[Tensor]): List of token tensors from different transformer layers.
                Each element has shape [B, S*P, 2*C]
            images (Tensor): Input images with shape [B, S, 3, H, W].
            patch_start_idx (int): Starting index for patch tokens.
            frames_start_idx (int, optional): Starting index for frames to process.
            frames_end_idx (int, optional): Ending index for frames to process.

        Returns:
            Tensor or Tuple[Tensor, Tensor]: Feature maps or (predictions, confidence).
                - Feature maps: [B, S, C, H, W]
                - Predictions: [B, S, 1, H, W]
                - Confidence: [B, S, 1, H, W]
        """
        # 如果处理帧的子集，则截取对应部分的图像
        if frames_start_idx is not None and frames_end_idx is not None:
            # images: [B, frames_end_idx - frames_start_idx, 3, H, W]
            images = images[:, frames_start_idx:frames_end_idx].contiguous()

        # images: [B, S_chunk, 3, H, W] 其中S_chunk是当前处理的帧数
        B, S_chunk, _, H, W = images.shape

        # 计算patch的高和宽
        patch_h, patch_w = H // self.patch_size, W // self.patch_size

        out = []
        dpt_idx = 0

        # 处理每个中间层的特征
        for layer_idx in self.intermediate_layer_idx:
            # x: [B, S_chunk*P, 2*C] 其中P是token数量
            x = aggregated_tokens_list[layer_idx][:, :, patch_start_idx:]

            # Select frames if processing a chunk
            if frames_start_idx is not None and frames_end_idx is not None:
                # x: [B, (frames_end_idx-frames_start_idx)*P, 2*C]
                x = x[:, frames_start_idx:frames_end_idx]

            # x: [B * S_chunk, P, 2*C]
            x = x.view(B * S_chunk, -1, x.shape[-1])

            # 归一化
            # x: [B * S_chunk, P, 2*C]
            x = self.norm(x)

            # 重塑为2D特征图
            # x: [B * S_chunk, 2*C, patch_h, patch_w]
            x = x.permute(0, 2, 1).reshape((x.shape[0], x.shape[-1], patch_h, patch_w))

            # 投影到目标通道数
            # x: [B * S_chunk, out_channels[dpt_idx], patch_h, patch_w]
            x = self.projects[dpt_idx](x)
            if self.pos_embed:
                # 添加位置嵌入
                x = self._apply_pos_embed(x, W, H)
            # 上采样到统一尺寸
            # x: [B * S_chunk, out_channels[dpt_idx], patch_h*stride, patch_w*stride]
            x = self.resize_layers[dpt_idx](x)

            # 添加到输出列表
            out.append(x)
            dpt_idx += 1

        # 融合多尺度特征
        # out: [B * S_chunk, features, patch_h*stride, patch_w*stride]
        out = self.scratch_forward(out)
        # 插值到目标分辨率
        # out: [B * S_chunk, features, H//down_ratio, W//down_ratio]
        out = custom_interpolate(
            out,
            (int(patch_h * self.patch_size / self.down_ratio), int(patch_w * self.patch_size / self.down_ratio)),
            mode="bilinear",
            align_corners=True,
        )

        if self.pos_embed:
            # 添加位置嵌入
            out = self._apply_pos_embed(out, W, H)

        if self.feature_only:
            # 返回特征图: [B, S_chunk, features, H//down_ratio, W//down_ratio]
            return out.view(B, S_chunk, *out.shape[1:])

        # 应用输出卷积
        # out: [B * S_chunk, output_dim, H//down_ratio, W//down_ratio]
        out = self.scratch.output_conv2(out)
        # 激活函数处理
        # preds: [B * S_chunk, 1, H//down_ratio, W//down_ratio]
        # conf: [B * S_chunk, 1, H//down_ratio, W//down_ratio]
        preds, conf = activate_head(out, activation=self.activation, conf_activation=self.conf_activation)

        # 重塑为最终输出格式
        # preds: [B, S_chunk, 1, H//down_ratio, W//down_ratio]
        # conf: [B, S_chunk, 1, H//down_ratio, W//down_ratio]
        preds = preds.view(B, S_chunk, *preds.shape[1:])
        conf = conf.view(B, S_chunk, *conf.shape[1:])
        return preds, conf

    def _apply_pos_embed(self, x: torch.Tensor, W: int, H: int, ratio: float = 0.1) -> torch.Tensor:
        """
        Apply positional embedding to tensor x.
        
        Args:
            x: 输入特征图 [B, C, H, W]
            W: 原始图像宽度
            H: 原始图像高度
            ratio: 位置嵌入缩放因子
            
        Returns:
            带位置嵌入的特征图 [B, C, H, W]
        """
        patch_w = x.shape[-1]
        patch_h = x.shape[-2]
        # pos_embed: [patch_w, patch_h, 2]
        pos_embed = create_uv_grid(patch_w, patch_h, aspect_ratio=W / H, dtype=x.dtype, device=x.device)
        # pos_embed: [C, patch_h, patch_w]
        pos_embed = position_grid_to_embed(pos_embed, x.shape[1])
        # pos_embed: [C, patch_h, patch_w]
        pos_embed = pos_embed * ratio
        # pos_embed: [1, C, patch_h, patch_w]
        pos_embed = pos_embed.permute(2, 0, 1)[None].expand(x.shape[0], -1, -1, -1)
        # 返回: [B, C, patch_h, patch_w]
        return x + pos_embed

    def scratch_forward(self, features: List[torch.Tensor]) -> torch.Tensor:
        """
        Forward pass through the fusion blocks.

        Args:
            features (List[Tensor]): List of feature maps from different layers.
                Each element has shape [B*S_chunk, channels, height, width]
                Typically 4 elements with increasing channels:
                - features[0]: [B*S_chunk, 256, h0, w0]
                - features[1]: [B*S_chunk, 512, h1, w1]
                - features[2]: [B*S_chunk, 1024, h2, w2]
                - features[3]: [B*S_chunk, 1024, h3, w3]

        Returns:
            Tensor: Fused feature map with shape [B*S_chunk, 256, h0, w0]
        """
        # layer_1: [B*S_chunk, 256, h0, w0]
        # layer_2: [B*S_chunk, 512, h1, w1]
        # layer_3: [B*S_chunk, 1024, h2, w2]
        # layer_4: [B*S_chunk, 1024, h3, w3]
        layer_1, layer_2, layer_3, layer_4 = features

        # 通过1x1卷积调整通道数
        # layer_1_rn: [B*S_chunk, 256, h0, w0]
        layer_1_rn = self.scratch.layer1_rn(layer_1)
        # layer_2_rn: [B*S_chunk, 256, h1, w1]
        layer_2_rn = self.scratch.layer2_rn(layer_2)
        # layer_3_rn: [B*S_chunk, 256, h2, w2]
        layer_3_rn = self.scratch.layer3_rn(layer_3)
        # layer_4_rn: [B*S_chunk, 256, h3, w3]
        layer_4_rn = self.scratch.layer4_rn(layer_4)

        # 自上而下融合特征
        # out: [B*S_chunk, 256, h2, w2]
        out = self.scratch.refinenet4(layer_4_rn, size=layer_3_rn.shape[2:])
        del layer_4_rn, layer_4

        # out: [B*S_chunk, 256, h1, w1]
        out = self.scratch.refinenet3(out, layer_3_rn, size=layer_2_rn.shape[2:])
        del layer_3_rn, layer_3

        # out: [B*S_chunk, 256, h0, w0]
        out = self.scratch.refinenet2(out, layer_2_rn, size=layer_1_rn.shape[2:])
        del layer_2_rn, layer_2

        # out: [B*S_chunk, 256, h0, w0]
        out = self.scratch.refinenet1(out, layer_1_rn)
        del layer_1_rn, layer_1

        # 最终输出卷积
        # out: [B*S_chunk, 256, h0, w0]
        out = self.scratch.output_conv1(out)
        return out


################################################################################
# Modules
################################################################################


def _make_fusion_block(features: int, size: int = None, has_residual: bool = True, groups: int = 1) -> nn.Module:
    return FeatureFusionBlock(
        features,
        nn.ReLU(inplace=True),
        deconv=False,
        bn=False,
        expand=False,
        align_corners=True,
        size=size,
        has_residual=has_residual,
        groups=groups,
    )


def _make_scratch(in_shape: List[int], out_shape: int, groups: int = 1, expand: bool = False) -> nn.Module:
    scratch = nn.Module()
    out_shape1 = out_shape
    out_shape2 = out_shape
    out_shape3 = out_shape
    if len(in_shape) >= 4:
        out_shape4 = out_shape

    if expand:
        out_shape1 = out_shape
        out_shape2 = out_shape * 2
        out_shape3 = out_shape * 4
        if len(in_shape) >= 4:
            out_shape4 = out_shape * 8

    scratch.layer1_rn = nn.Conv2d(
        in_shape[0], out_shape1, kernel_size=3, stride=1, padding=1, bias=False, groups=groups
    )
    scratch.layer2_rn = nn.Conv2d(
        in_shape[1], out_shape2, kernel_size=3, stride=1, padding=1, bias=False, groups=groups
    )
    scratch.layer3_rn = nn.Conv2d(
        in_shape[2], out_shape3, kernel_size=3, stride=1, padding=1, bias=False, groups=groups
    )
    if len(in_shape) >= 4:
        scratch.layer4_rn = nn.Conv2d(
            in_shape[3], out_shape4, kernel_size=3, stride=1, padding=1, bias=False, groups=groups
        )
    return scratch


class ResidualConvUnit(nn.Module):
    """Residual convolution module."""

    def __init__(self, features, activation, bn, groups=1):
        """Init.

        Args:
            features (int): number of features
        """
        super().__init__()

        self.bn = bn
        self.groups = groups
        self.conv1 = nn.Conv2d(features, features, kernel_size=3, stride=1, padding=1, bias=True, groups=self.groups)
        self.conv2 = nn.Conv2d(features, features, kernel_size=3, stride=1, padding=1, bias=True, groups=self.groups)

        self.norm1 = None
        self.norm2 = None

        self.activation = activation
        self.skip_add = nn.quantized.FloatFunctional()

    def forward(self, x):
        """Forward pass.

        Args:
            x (tensor): input with shape [B, C, H, W]

        Returns:
            tensor: output with shape [B, C, H, W]
        """

        out = self.activation(x)
        out = self.conv1(out)
        if self.norm1 is not None:
            out = self.norm1(out)

        out = self.activation(out)
        out = self.conv2(out)
        if self.norm2 is not None:
            out = self.norm2(out)

        return self.skip_add.add(out, x)


class FeatureFusionBlock(nn.Module):
    """Feature fusion block."""

    def __init__(
        self,
        features,
        activation,
        deconv=False,
        bn=False,
        expand=False,
        align_corners=True,
        size=None,
        has_residual=True,
        groups=1,
    ):
        """Init.

        Args:
            features (int): number of features
        """
        super(FeatureFusionBlock, self).__init__()

        self.deconv = deconv
        self.align_corners = align_corners
        self.groups = groups
        self.expand = expand
        out_features = features
        if self.expand == True:
            out_features = features // 2

        self.out_conv = nn.Conv2d(
            features, out_features, kernel_size=1, stride=1, padding=0, bias=True, groups=self.groups
        )

        if has_residual:
            self.resConfUnit1 = ResidualConvUnit(features, activation, bn, groups=self.groups)

        self.has_residual = has_residual
        self.resConfUnit2 = ResidualConvUnit(features, activation, bn, groups=self.groups)

        self.skip_add = nn.quantized.FloatFunctional()
        self.size = size

    def forward(self, *xs, size=None):
        """Forward pass.

        Args:
            *xs: Variable number of input tensors, typically 1 or 2 tensors with shape [B, C, H, W]
            size: Target size for interpolation

        Returns:
            tensor: output with shape [B, C, H*2, W*2] (typically upsampled by factor of 2)
        """
        # xs[0]: 主输入特征 [B, C, H, W]
        output = xs[0]

        # 如果有残差连接且提供了第二个输入
        if self.has_residual:
            # xs[1]: 残差输入特征 [B, C, H, W]
            res = self.resConfUnit1(xs[1])
            # 将主输入和残差输入相加
            output = self.skip_add.add(output, res)

        # 应用第二个残差卷积单元
        output = self.resConfUnit2(output)

        # 插值到目标尺寸
        if (size is None) and (self.size is None):
            modifier = {"scale_factor": 2}
        elif size is None:
            modifier = {"size": self.size}
        else:
            modifier = {"size": size}

        # 上采样特征图
        output = custom_interpolate(output, **modifier, mode="bilinear", align_corners=self.align_corners)
        # 1x1卷积调整通道数
        output = self.out_conv(output)

        return output


def custom_interpolate(
    x: torch.Tensor,
    size: Tuple[int, int] = None,
    scale_factor: float = None,
    mode: str = "bilinear",
    align_corners: bool = True,
) -> torch.Tensor:
    """
    Custom interpolate to avoid INT_MAX issues in nn.functional.interpolate.
    
    Args:
        x: 输入张量 [B, C, H, W]
        size: 目标尺寸 (H, W)
        scale_factor: 缩放因子
        mode: 插值模式
        align_corners: 是否对齐角点
        
    Returns:
        插值后的张量 [B, C, H', W']
    """
    if size is None:
        size = (int(x.shape[-2] * scale_factor), int(x.shape[-1] * scale_factor))

    INT_MAX = 1610612736

    input_elements = size[0] * size[1] * x.shape[0] * x.shape[1]

    if input_elements > INT_MAX:
        chunks = torch.chunk(x, chunks=(input_elements // INT_MAX) + 1, dim=0)
        interpolated_chunks = [
            nn.functional.interpolate(chunk, size=size, mode=mode, align_corners=align_corners) for chunk in chunks
        ]
        x = torch.cat(interpolated_chunks, dim=0)
        return x.contiguous()
    else:
        return nn.functional.interpolate(x, size=size, mode=mode, align_corners=align_corners)


class MultiScaleDPTHead(nn.Module):
    """
    Multi-Scale DPT Head for extracting feature pyramids at multiple resolutions.

    This head extracts features at 1/4, 1/8, 1/16, and 1/32 resolutions from VGGT tokens
    for downstream stereo matching tasks, similar to Monster Plus architecture.

    Args:
        dim_in (int): Input dimension (channels) from VGGT tokens.
        patch_size (int): Patch size used in VGGT. Default is 14.
        features (int): Feature channels for intermediate representations. Default is 256.
        out_channels (List[int]): Output channels for each scale [1/4, 1/8, 1/16, 1/32].
        intermediate_layer_idx (List[int]): Indices of layers from aggregated tokens used for feature extraction.
        pos_embed (bool): Whether to use positional embedding. Default is True.
    """

    def __init__(
        self,
        dim_in: int,
        patch_size: int = 14,
        features: int = 256,
        out_channels: List[int] = [96, 192, 384, 768],  # Similar to Monster Plus vitb config
        intermediate_layer_idx: List[int] = [4, 11, 17, 23],
        pos_embed: bool = True,
    ) -> None:
        super(MultiScaleDPTHead, self).__init__()

        self.patch_size = patch_size
        self.pos_embed = pos_embed
        self.intermediate_layer_idx = intermediate_layer_idx
        self.out_channels = out_channels

        self.norm = nn.LayerNorm(dim_in)

        # Projection layers for each scale
        self.projects = nn.ModuleList([
            nn.Conv2d(
                in_channels=dim_in,
                out_channels=oc,
                kernel_size=1,
                stride=1,
                padding=0,
            )
            for oc in out_channels
        ])

        # Scale-specific processing layers
        # These will process features at different resolutions
        self.scale_processors = nn.ModuleList([
            # 1/4 scale processor (highest resolution)
            nn.Sequential(
                nn.Conv2d(out_channels[0], out_channels[0], 3, 1, 1),
                nn.ReLU(inplace=True),
                nn.Conv2d(out_channels[0], out_channels[0], 3, 1, 1),
            ),
            # 1/8 scale processor
            nn.Sequential(
                nn.Conv2d(out_channels[1], out_channels[1], 3, 1, 1),
                nn.ReLU(inplace=True),
                nn.Conv2d(out_channels[1], out_channels[1], 3, 1, 1),
            ),
            # 1/16 scale processor
            nn.Sequential(
                nn.Conv2d(out_channels[2], out_channels[2], 3, 1, 1),
                nn.ReLU(inplace=True),
                nn.Conv2d(out_channels[2], out_channels[2], 3, 1, 1),
            ),
            # 1/32 scale processor
            nn.Sequential(
                nn.Conv2d(out_channels[3], out_channels[3], 3, 1, 1),
                nn.ReLU(inplace=True),
                nn.Conv2d(out_channels[3], out_channels[3], 3, 1, 1),
            ),
        ])

        # Downsampling layers to create pyramid
        self.downsample_layers = nn.ModuleList([
            nn.Identity(),  # 1/4 scale - no downsampling
            nn.Conv2d(out_channels[0], out_channels[1], 3, 2, 1),  # 1/4 -> 1/8
            nn.Conv2d(out_channels[1], out_channels[2], 3, 2, 1),  # 1/8 -> 1/16
            nn.Conv2d(out_channels[2], out_channels[3], 3, 2, 1),  # 1/16 -> 1/32
        ])

    def forward(
        self,
        aggregated_tokens_list: List[torch.Tensor],
        images: torch.Tensor,
        patch_start_idx: int,
        frames_chunk_size: int = 8,
    ) -> List[torch.Tensor]:
        """
        Forward pass to extract multi-scale feature pyramids.

        Args:
            aggregated_tokens_list: List of token tensors from different transformer layers.
            images: Input images with shape [B, S, 3, H, W].
            patch_start_idx: Starting index for patch tokens.
            frames_chunk_size: Number of frames to process in each chunk.

        Returns:
            List[torch.Tensor]: Multi-scale features [feat_1/4, feat_1/8, feat_1/16, feat_1/32]
                Each element has shape [B, S, C_i, H_i, W_i] where:
                - feat_1/4: [B, S, out_channels[0], H/4, W/4]
                - feat_1/8: [B, S, out_channels[1], H/8, W/8]
                - feat_1/16: [B, S, out_channels[2], H/16, W/16]
                - feat_1/32: [B, S, out_channels[3], H/32, W/32]
        """
        B, S, _, H, W = images.shape

        # If frames_chunk_size is not specified or greater than S, process all frames at once
        if frames_chunk_size is None or frames_chunk_size >= S:
            return self._forward_impl(aggregated_tokens_list, images, patch_start_idx)

        # Process frames in chunks
        all_pyramid_features = [[] for _ in range(len(self.out_channels))]

        for frames_start_idx in range(0, S, frames_chunk_size):
            frames_end_idx = min(frames_start_idx + frames_chunk_size, S)

            # Process chunk and get pyramid features
            chunk_pyramid = self._forward_impl(
                aggregated_tokens_list, images, patch_start_idx, frames_start_idx, frames_end_idx
            )

            # Collect features for each scale
            for scale_idx, scale_features in enumerate(chunk_pyramid):
                all_pyramid_features[scale_idx].append(scale_features)

        # Concatenate results along the sequence dimension
        pyramid_features = []
        for scale_idx in range(len(self.out_channels)):
            pyramid_features.append(torch.cat(all_pyramid_features[scale_idx], dim=1))

        return pyramid_features

    def _forward_impl(
        self,
        aggregated_tokens_list: List[torch.Tensor],
        images: torch.Tensor,
        patch_start_idx: int,
        frames_start_idx: Optional[int] = None,
        frames_end_idx: Optional[int] = None,
    ) -> List[torch.Tensor]:
        """
        Implementation of the forward pass to extract multi-scale features.

        Args:
            aggregated_tokens_list: List of token tensors from different transformer layers.
            images: Input images with shape [B, S, 3, H, W].
            patch_start_idx: Starting index for patch tokens.
            frames_start_idx: Starting index for frames to process.
            frames_end_idx: Ending index for frames to process.

        Returns:
            List[torch.Tensor]: Multi-scale features [feat_1/4, feat_1/8, feat_1/16, feat_1/32]
        """
        # Handle frame chunking
        if frames_start_idx is not None and frames_end_idx is not None:
            images = images[:, frames_start_idx:frames_end_idx].contiguous()

        B, S_chunk, _, H, W = images.shape
        patch_h, patch_w = H // self.patch_size, W // self.patch_size

        # Extract features from the first intermediate layer for base feature map
        base_layer_idx = self.intermediate_layer_idx[0]
        x = aggregated_tokens_list[base_layer_idx][:, :, patch_start_idx:]

        # Select frames if processing a chunk
        if frames_start_idx is not None and frames_end_idx is not None:
            x = x[:, frames_start_idx:frames_end_idx]

        # Reshape to process tokens: [B * S_chunk, P, 2*C]
        x = x.view(B * S_chunk, -1, x.shape[-1])

        # Normalize and reshape to spatial format
        x = self.norm(x)  # [B * S_chunk, P, 2*C]
        x = x.permute(0, 2, 1).reshape((x.shape[0], x.shape[-1], patch_h, patch_w))  # [B * S_chunk, 2*C, patch_h, patch_w]

        # Project to base feature dimension
        base_features = self.projects[0](x)  # [B * S_chunk, out_channels[0], patch_h, patch_w]

        # Apply positional embedding if enabled
        if self.pos_embed:
            base_features = self._apply_pos_embed(base_features, W, H)

        # Upsample base features to 1/4 resolution (target resolution for first scale)
        target_h, target_w = H // 4, W // 4
        feat_1_4 = custom_interpolate(
            base_features,
            size=(target_h, target_w),
            mode="bilinear",
            align_corners=True
        )  # [B * S_chunk, out_channels[0], H/4, W/4]

        # Process 1/4 scale features
        feat_1_4 = self.scale_processors[0](feat_1_4)

        # Create feature pyramid by progressive downsampling
        pyramid_features = []
        current_feat = feat_1_4

        for scale_idx in range(len(self.out_channels)):
            if scale_idx == 0:
                # 1/4 scale - already processed
                processed_feat = current_feat
            else:
                # Downsample and project to next scale
                current_feat = self.downsample_layers[scale_idx](current_feat)
                processed_feat = self.scale_processors[scale_idx](current_feat)

            # Reshape to final output format: [B, S_chunk, C, H, W]
            final_feat = processed_feat.view(B, S_chunk, *processed_feat.shape[1:])
            pyramid_features.append(final_feat)

            # Update current features for next iteration
            current_feat = processed_feat

        return pyramid_features

    def _apply_pos_embed(self, x: torch.Tensor, W: int, H: int, ratio: float = 0.1) -> torch.Tensor:
        """
        Apply positional embedding to tensor x.

        Args:
            x: Input feature map [B, C, H, W]
            W: Original image width
            H: Original image height
            ratio: Position embedding scaling factor

        Returns:
            Feature map with positional embedding [B, C, H, W]
        """
        patch_w = x.shape[-1]
        patch_h = x.shape[-2]
        pos_embed = create_uv_grid(patch_w, patch_h, aspect_ratio=W / H, dtype=x.dtype, device=x.device)
        pos_embed = position_grid_to_embed(pos_embed, x.shape[1])
        pos_embed = pos_embed * ratio
        pos_embed = pos_embed.permute(2, 0, 1)[None].expand(x.shape[0], -1, -1, -1)
        return x + pos_embed