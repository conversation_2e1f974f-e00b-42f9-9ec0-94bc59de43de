#!/usr/bin/env python3
"""
简化的VGGT模型测试脚本
根据修改后的vggt.py生成，只测试基本的导入和推理功能
"""

import os
import sys
import torch

# 设置路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, 'vggt'))

def load_model_weights(model, weight_path):
    """
    加载模型权重并验证参数匹配
    
    规则:
    1. 权重文件中存在但模型中不存在的参数 - 正常，忽略
    2. 模型中存在但权重文件中不存在的参数 - 报错
    3. 都存在的参数但维度不匹配 - 报错
    """
    print(f"📥 加载权重文件: {weight_path}")
    
    if not os.path.exists(weight_path):
        raise FileNotFoundError(f"权重文件不存在: {weight_path}")
    
    # 加载权重
    checkpoint = torch.load(weight_path, map_location='cpu')
    state_dict = checkpoint if isinstance(checkpoint, dict) else checkpoint['state_dict']
    
    model_dict = model.state_dict()
    loaded_params = {}
    mismatched_params = []
    
    # 检查权重文件中的参数
    for name, param in state_dict.items():
        if name in model_dict:
            # 参数在模型中存在，检查维度是否匹配
            if param.shape == model_dict[name].shape:
                loaded_params[name] = param
            else:
                mismatched_params.append((name, param.shape, model_dict[name].shape))
    
    # 检查模型中是否存在权重文件中没有的参数
    missing_params = []
    for name in model_dict.keys():
        if name not in state_dict:
            missing_params.append(name)
    
    # 报告检查结果
    print(f"📊 权重文件参数总数: {len(state_dict)}")
    print(f"📊 模型参数总数: {len(model_dict)}")
    print(f"✅ 成功加载参数: {len(loaded_params)}")
    
    if mismatched_params:
        print("❌ 维度不匹配的参数:")
        for name, weight_shape, model_shape in mismatched_params[:5]:  # 只显示前5个
            print(f"   {name}: 权重文件 {weight_shape} vs 模型 {model_shape}")
        raise ValueError(f"发现 {len(mismatched_params)} 个参数维度不匹配")
    
    if missing_params:
        print("❌ 模型中存在但权重文件中缺失的参数:")
        for name in missing_params[:10]:  # 只显示前10个
            print(f"   {name}")
        raise ValueError(f"权重文件缺失 {len(missing_params)} 个模型必需的参数")
    
    # 加载匹配的参数
    model.load_state_dict(loaded_params, strict=False)
    print("✅ 权重加载完成")

def main():
    print("🔧 VGGT模型简单测试")
    print("=" * 40)
    
    # 1. 环境信息
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"设备: {device}")
    print(f"PyTorch: {torch.__version__}")
    
    # 2. 导入模型
    print("\n📦 导入模型...")
    try:
        sys.path.append('./vggt/vggt')
        from models.vggt import VGGT
        print("✓ VGGT模型导入成功")
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return
    
    # 3. 创建模型
    print("\n🏗️ 创建模型...")
    try:
        model = VGGT()
        model = model.to(device)
        model.eval()
        print("✓ 模型创建成功")
        
        # 统计参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"✓ 参数数量: {total_params:,}")
        
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
        return
    
    # 4. 加载权重
    print("\n📂 加载权重...")
    try:
        weight_path = "vggt/checkpoint/model.pt"
        load_model_weights(model, weight_path)
        print("✓ 权重加载成功")
    except Exception as e:
        print(f"✗ 权重加载失败: {e}")
        return
    
    # 5. 创建随机输入
    print("\n📊 准备输入数据...")
    try:
        # 创建随机输入 (批次=1, 序列=2, 通道=3, 高度=518, 宽度=518)
        batch_size = 1
        sequence_length = 2  # 立体对
        height, width = 518, 518
        
        images = torch.randn(batch_size, sequence_length, 3, height, width).to(device)
        print(f"✓ 输入图像: {images.shape}")
        print(f"✓ 输入范围: [{images.min():.3f}, {images.max():.3f}]")
        
    except Exception as e:
        print(f"✗ 输入创建失败: {e}")
        return
    
    # 6. 模型推理
    print("\n🚀 开始推理...")
    try:
        with torch.no_grad():
            outputs = model(images)
        
        print("✓ 推理成功")
        
    except Exception as e:
        print(f"✗ 推理失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 7. 打印输出维度
    print("\n📋 输出结果:")
    print("-" * 30)
    
    for key, value in outputs.items():
        if hasattr(value, 'shape'):
            print(f"{key:15s}: {str(value.shape):25s} | 数据类型: {value.dtype}")
            
            # 打印数值范围
            if hasattr(value, 'min') and hasattr(value, 'max'):
                try:
                    min_val = value.min().item()
                    max_val = value.max().item()
                    print(f"{'':15s}  范围: [{min_val:.4f}, {max_val:.4f}]")
                except:
                    pass
        else:
            print(f"{key:15s}: {type(value):25s}")
        print()
    
    # 8. 验证输出
    print("✅ 验证结果:")
    expected_outputs = ['depth', 'depth_conf', 'images']
    
    for expected in expected_outputs:
        if expected in outputs:
            print(f"✓ {expected} - 存在")
        else:
            print(f"✗ {expected} - 缺失")
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        import traceback
        traceback.print_exc()